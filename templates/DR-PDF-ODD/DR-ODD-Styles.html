<style type="text/css">
  @page {
    margin: 20px;
    size: A4 portrait;
  }

  * {
    font-family: sans-serif;
    font-size: 11px;
  }

  .bold {
    font-weight: 700;
  }

  table {
    min-width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    word-wrap: break-word;
    margin-bottom: 50px;

    .subText {
      background-color: #d5dce4;
      font-style: italic;
    }

    tr {
      height: 38px;
      page-break-inside: avoid;
    }

    td {
      border: 1px solid black;
      padding: 5px 10px;

      &.titleColumn {
        background-color: #d5dce4;
        font-weight: 700;
        width: 20%;
      }
    }
  }

  .body {
    background-color: white;
    counter-reset: page;
  }

  .page {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
  }

  .pageHeader {
    width: 100%;
    line-height: 18px;
    font-size: 12px;
    position: relative;
    padding-top: 5px;
    text-align: center;
    padding-bottom: 10px;

    .NumberToName {
      float: left;
      width: 50%;
      text-wrap: wrapped;
      text-align: left;
    }

    .fileCompleted {
      float: right;
      width: 50%;
      text-align: right;
    }
  }

  .mainTitle {
    background-color: #c00000;
    color: white;
    text-align: center;
    margin-top: 30px;
    font-size: 19px;
    padding: 1.5px;
    text-wrap: wrap;
  }

  .section1 {
    table {
      .valueColumn {
        width: 30%;
      }
    }

    .extraText {
      margin: 0 35px 20px;
      margin-bottom: 20px;
    }

    .contents {
      margin: 0 35px;

      b {
        text-decoration: underline;
        line-height: 20px;
      }

      a {
        color: black;
        text-decoration: none;
      }
    }
  }

  .section2 {
    margin-top: 40px;

    .headerSubText {
      margin-top: -18px;
    }

    table {
      .valueColumn {
        min-width: 240px;
      }

      .tableHeadRow {
        background-color: #d5dce4;
        font-weight: 700;
        text-align: center;
      }
    }
  }

  .section3 {
    margin-top: 40px;

    .subTitle {
      font-size: 16px;
      font-weight: 700;
      padding: 1.5px;
    }

    table {
      .titleRow {
        font-weight: 700;
        width: 32%;
      }

      .craCell {
        width: 34%;
      }

      .riskCell {
        width: 25%;
      }

      .tableHeadRow {
        background-color: #d5dce4;
        font-weight: 700;
        text-align: left;
      }

    }
  }

  .section4 {
    margin-top: 40px;

    table {
      .titleColumn {
        width: 25%;

        .extraText {
          font-weight: 400;
          font-style: italic;
        }
      }

      &.cma {
        .titleColumn {
          width: 10%;
        }
      }

      .titleColumnLong {
        width: 40%;
        background-color: #d5dce4;
        font-weight: 700;
      }

      .valueColumnHalf {
        width: 160px;
      }

      .tableHeadRow {
        background-color: #d5dce4;
        font-weight: 700;
        text-align: center;
        margin-top: -2px;
        margin-bottom: 0px;
      }

      .subText {
        margin-bottom: -2px;
      }

      p {
        margin: 0px;
      }

      .subtitleRow {
        height: 20px;
        padding: 0px;
      }
    }
  }

  .section5 {
    table {
      &.fixed {
        width: 100%;
      }

      .titleColumn {
        text-align: center;
      }

      .singleColumn {
        width: 30%;
        background-color: #d5dce4;
        font-weight: 700;
      }

      .valueRow {
        overflow-wrap: break-word;
      }
    }

    .tableTitle {
      margin-bottom: 5px;
    }

    .miniTitle {
      font-size: 16px;
      text-decoration: underline;
    }

    .empty {
      border: none;
    }
  }

  .section6 {
    margin-top: 40px;

    .headerSubText {
      margin-top: -18px;
    }

    table {
      &.firstTable {
        td {
          vertical-align: top;

          &.titleColumn {
            .extraText {
              font-weight: 400;
              font-style: italic;
            }
          }
        }
      }

      .titleColumn {
        width: 40%;
      }

      .tableHeadRow {
        background-color: #d5dce4;
        font-weight: 700;
        text-align: center;
      }

      .empty {
        border: none;
        min-width: 147px;
      }
    }
  }

  .pageFooter {
    width: 100%;
    margin-top: 50px;
    background-color: #c00000;
    color: #ffffff;
    text-align: center;
    break-after: page;
    font-size: 13px;
    font-weight: 700;
  }

  .section7 {
    margin-top: 40px;

    table {
      .subTitle {
        font-weight: 700;
        font-style: normal;
        padding: 5px;
        background-color: #d5dce4;
        text-align: center;
      }

      .valueColumn {
        width: 427px;
      }

      .cashType {
        background-color: #d5dce4;
        font-weight: 700;
      }

      .question {
        background-color: #d5dce4;
        font-weight: 700;
        width: 50%;

        &.cash {
          width: 38%;
        }
      }
    }
  }

  .section8 {
    margin-top: 40px;

    .subTitle {
      font-style: italic;
      font-weight: 400;
    }

    table {
      tr {
        padding: 5px;
        width: 67px;
      }

      .plausibility {
        font-weight: 700;
        font-style: normal;
        padding: 5px;
        background-color: #d5dce4;
        text-align: left;
      }

      .subTitleRow {
        font-weight: 700;
        font-style: normal;
        padding: 5px;
        background-color: #d5dce4;
        text-align: center;
      }

      .tableTitle {
        font-weight: 700;
        width: 67px;
        font-size: 13px;
        background-color: #d5dce4;
        text-align: center;
      }

      .cashMain {
        width: 20%;
        font-weight: 700;
        background-color: #d5dce4;
      }

      .parties {
        background-color: #d5dce4;
      }

      .question {
        width: 40%;
        font-weight: 700;
        background-color: #d5dce4;
      }

      .rationale {
        font-weight: 700;
        background-color: #d5dce4;
      }
    }
  }

  .section9 {
    table {
      .titleColumn {
        text-align: center;
      }

      .titleColumns {
        width: 147px;
        background-color: #d5dce4;
        font-weight: 700;
      }

      .valueColumn {
        min-width: 240px;
      }

      p {
        line-height: 13px;
        margin: 2px;
      }
    }

    .noMedia {
      font-size: 13px;
      text-align: center;
      background-color: #d5dce4;
      font-weight: 700;
      border: 1px solid black;
    }
  }

  .section10 {
    table {
      tr {
        height: 20px;
      }

      .subTitle {
        font-weight: 700;
        background-color: #d5dce4;
        padding: 5px;
        text-align: center;
      }

      .type {
        width: 60px;
        background-color: #d5dce4;
        font-weight: 700;
      }

      .escalationDetailHeader {
        width: 133px;
        background-color: #d5dce4;
        font-weight: 700;
      }
    }

    .noEscalations {
      font-size: 13px;
      text-align: center;
      background-color: #d5dce4;
      font-weight: 700;
      border: 1px solid black;
    }
  }


  .section11 {
    table {
      table-layout: auto;
      .sanctionName {
        width: 20%;
        background-color: #d5dce4;
        font-weight: 700;
        text-align: left;
      }

      .textDescription {
        width: 40%;
        background-color: #d5dce4;
        font-weight: 700;
      }

      .tableHeadRow {
        background-color: #d5dce4;
        font-weight: 700;
        text-align: center;
      }
      .titleColumn {
        width: 25%;
        background-color: #d5dce4;
        .extraText {
          font-weight: 400;
          font-style: italic;
        }

        &.w20 {
          width: 20% !important;
        }

        &.w15 {
          width: 15% !important;
        }

        &.w28 {
          width: 28% !important;
        }
      }

      &.cma {
        .titleColumn {
          width: 10%;
        }
      }

      .titleColumnLong {
        width: 40%;
        background-color: #d5dce4;
        font-weight: 700;
      }

      .valueColumnHalf {
        width: 160px;
      }

      .tableSubHeaderRow {
        background-color: #d5dce4;
        font-weight: 700;
        text-align: center;
      }

      .question {
        width: 40%;
        font-weight: 700;
        background-color: #d5dce4;
        height: 40px;
      }

      .h26 {
        height: 26px !important;
      }

      .p5 {
        padding: 5px 0px 0px 5px;
      }
    }
  }
</style>